# === Spring Boot ===
SERVER_PORT=8080

# === Database ===
MYSQL_URL=jdbc:mysql://<数据库地址>:<数据库端口>/<数据库名>?useUnicode=yes&characterEncoding=UTF-8&useInformationSchema=true&useSSL=false&serverTimezone=UTC
MYSQL_USER=<数据库用户名>
MYSQL_PASSWORD=<数据库密码>

# === JWT ===
JWT_SECRET=<jwt secret>
JWT_REASON_SECRET=<jwt reason secret>
JWT_RESET_SECRET=<jwt reset secret>
JWT_INVITE_SECRET=<jwt invite secret>
JWT_EXPIRATION=2592000000

# === Redis ===
REDIS_HOST=<Redis 地址>
REDIS_PORT=<Redis 端口>

# === Resend ===
RESEND_API_KEY=<你的resend-api-key>
RESEND_FROM_EMAIL=<你的 resend 发送邮箱>

# === COS ===
# COS_BASE_URL=https://<你的cos>.cos.ap-guangzhou.myqcloud.com
COS_BASE_URL=https://<你的cos>.cos.accelerate.myqcloud.com
COS_SECRET_ID=<你的cos-secret-id>
COS_SECRET_KEY=<你的cos-secret-key>
COS_BUCKET_NAME=<你的cos-bucket-name>

# === OAuth ===
GOOGLE_CLIENT_ID=<你的google-client-id>
GITHUB_CLIENT_ID=<你的github-client-id>
GITHUB_CLIENT_SECRET=<你的github-client-secret>
TWITTER_CLIENT_ID=<你的twitter-client-id>
TWITTER_CLIENT_SECRET=<你的-twitter-client-secret>
DISCORD_CLIENT_ID=<你的discord-client-id>
DISCORD_CLIENT_SECRET=<你的discord-client-secret>
TELEGRAM_BOT_TOKEN=<你的telegram-bot-token>

# === OPENAI ===
OPENAI_API_KEY=<你的openai-api-key>

# === Webpush ===
WEBPUSH_PUBLIC_KEY=<你的webpush-public-key>
WEBPUSH_PRIVATE_KEY=<你的webpush-private-key>

# === RabbitMQ ===
RABBITMQ_HOST=<你的rabbitmq_host>
RABBITMQ_PORT=<你的rabbitmq_port>
RABBITMQ_USERNAME=<你的rabbitmq_username>
RABBITMQ_PASSWORD=<你的rabbitmq_password>

# LOG_LEVEL=DEBUG
