# for spring boot
server.port=${SERVER_PORT:8080}

# for mysql
logging.level.root=${LOG_LEVEL:INFO}
logging.level.com.openisle.service.CosImageUploader=DEBUG
spring.datasource.url=${MYSQL_URL:************************************}
spring.datasource.username=${MYSQL_USER:root}
spring.datasource.password=${MYSQL_PASSWORD:password}
spring.jpa.hibernate.ddl-auto=update

# for redis
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.database=${REDIS_DATABASE:0}

# for jwt
app.jwt.secret=${JWT_SECRET:jwt_sec}
app.jwt.reason-secret=${JWT_REASON_SECRET:jwt_reason_sec}
app.jwt.reset-secret=${JWT_RESET_SECRET:jwt_reset_sec}
app.jwt.invite-secret=${JWT_INVITE_SECRET:jwt_invite_sec}
# 30 days
app.jwt.expiration=${JWT_EXPIRATION:2592000000}
# Password strength: LOW, MEDIUM or HIGH
app.password.strength=${PASSWORD_STRENGTH:LOW}

# Post publish mode: DIRECT or REVIEW
app.post.publish-mode=${POST_PUBLISH_MODE:DIRECT}

# User register mode: DIRECT or WHITELIST
app.register.mode=${REGISTER_MODE:WHITELIST}

# Image upload configuration
app.upload.check-type=${UPLOAD_CHECK_TYPE:true}
app.upload.max-size=${UPLOAD_MAX_SIZE:5242880}

# Default avatar generator configuration
app.avatar.style=${AVATAR_STYLE:pixel-art-neutral}
app.avatar.size=${AVATAR_SIZE:128}
app.avatar.base-url=${AVATAR_BASE_URL:https://api.dicebear.com/6.x}

# Default list size for user posts and replies
app.user.posts-limit=${USER_POSTS_LIMIT:10}
app.user.replies-limit=${USER_REPLIES_LIMIT:50}
# Length of extracted snippets for posts and search (-1 to disable truncation)
app.snippet-length=${SNIPPET_LENGTH:50}

# Captcha configuration
app.captcha.enabled=${CAPTCHA_ENABLED:false}
recaptcha.secret-key=${RECAPTCHA_SECRET_KEY:}
app.captcha.register-enabled=${CAPTCHA_REGISTER_ENABLED:false}
app.captcha.login-enabled=${CAPTCHA_LOGIN_ENABLED:false}
app.captcha.post-enabled=${CAPTCHA_POST_ENABLED:false}
app.captcha.comment-enabled=${CAPTCHA_COMMENT_ENABLED:false}

# ========= Optional =========
# for resend email send service, you can improve your service by yourself
resend.api.key=${RESEND_API_KEY:}
resend.from.email=${RESEND_FROM_EMAIL:}
# your email services: ...


# for tencent cloud image upload service, you can improve your service by yourself
cos.base-url=${:https://example.com}
cos.secret-id=${COS_SECRET_ID:}
cos.secret-key=${COS_SECRET_KEY:}
cos.region=${COS_REGION:ap-guangzhou}
cos.bucket-name=${COS_BUCKET_NAME:}
# your image upload services: ...

# Google OAuth configuration
google.client-id=${GOOGLE_CLIENT_ID:}
# GitHub OAuth configuration
github.client-id=${GITHUB_CLIENT_ID:}
github.client-secret=${GITHUB_CLIENT_SECRET:}
# Discord OAuth configuration
discord.client-id=${DISCORD_CLIENT_ID:}
discord.client-secret=${DISCORD_CLIENT_SECRET:}
# Twitter OAuth configuration
twitter.client-id=${TWITTER_CLIENT_ID:}
twitter.client-secret=${TWITTER_CLIENT_SECRET:}
# Telegram login configuration
telegram.bot-token=${TELEGRAM_BOT_TOKEN:}
# OpenAI configuration
openai.api-key=${OPENAI_API_KEY:}
openai.model=${OPENAI_MODEL:gpt-4o}
# AI markdown format usage limit per user per day (-1 for unlimited)
app.ai.format-limit=${AI_FORMAT_LIMIT:3}

# Website URL for emails and redirects
app.website-url=${WEBSITE_URL:https://www.open-isle.com}

# Web push configuration
app.webpush.public-key=${WEBPUSH_PUBLIC_KEY:}
app.webpush.private-key=${WEBPUSH_PRIVATE_KEY:}

# RabbitMQ Configuration
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}

# RabbitMQ éåéç½® - ä¿®æ¹ä¸ºéæä¹åä»¥å¹éç°æéå
rabbitmq.queue.durable=true
rabbitmq.sharding.enabled=true

# springdoc-openapi-starter-webmvc-api
# see https://springdoc.org/#springdoc-openapi-core-properties
springdoc.api-docs.path=/api/v3/api-docs
springdoc.api-docs.enabled=true
springdoc.api-docs.servers[0].url=https://www.open-isle.com
springdoc.api-docs.servers[0].description=Production Environment
springdoc.api-docs.servers[1].url=https://www.staging.open-isle.com
springdoc.api-docs.servers[1].description=Staging Environment
springdoc.info.title=OpenIsle
springdoc.info.description=OpenIsle Open API Documentation
springdoc.info.version=0.0.1
springdoc.info.scheme=Bearer
springdoc.info.header=Authorization
