spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=create-drop

resend.api.key=dummy
cos.base-url=http://test.example.com
cos.secret-id=dummy
cos.secret-key=dummy
cos.region=ap-guangzhou
cos.bucket-name=testbucket

# Image upload configuration for tests
app.upload.check-type=true
app.upload.max-size=1048576

app.jwt.secret=TestSecret
app.jwt.reason-secret=TestReasonSecret
app.jwt.reset-secret=TestResetSecret
app.jwt.expiration=3600000

# Default publish mode for tests
app.post.publish-mode=DIRECT

# Base website URL used in tests
app.website-url=http://localhost

# Default avatar generator configuration
app.avatar.style=${AVATAR_STYLE:pixel-art-neutral}
app.avatar.size=${AVATAR_SIZE:128}
app.avatar.base-url=${AVATAR_BASE_URL:https://api.dicebear.com/6.x}

# Web push configuration
app.webpush.public-key=${WEBPUSH_PUBLIC_KEY:}
app.webpush.private-key=${WEBPUSH_PRIVATE_KEY:}
