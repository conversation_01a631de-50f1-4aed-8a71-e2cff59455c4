{"name": "openisle-docs", "version": "0.0.0", "private": true, "scripts": {"postinstall": "fumadocs-mdx", "build": "next build", "dev": "next dev --turbo -p 7890", "start": "next start", "generate": "bun ./scripts/generate-docs.ts"}, "dependencies": {"fumadocs-core": "15.7.7", "fumadocs-mdx": "11.8.2", "fumadocs-openapi": "^9.3.4", "fumadocs-ui": "15.7.7", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "^19.1.1", "react-dom": "^19.1.1", "shiki": "^3.12.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/mdx": "^2.0.13", "@types/node": "24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}