import { defineNuxtPlugin } from 'nuxt/app'
import {
  Pin,
  Fireworks,
  Gift,
  RankingList,
  Star,
  Edit,
  HashtagKey,
  Remind,
  Info,
  ChartLine,
  Finance,
  Up,
  Down,
  TagOne,
  MedalOne,
  Next,
  DropDownList,
  MoreOne,
  SunOne,
  Moon,
  ComputerOne,
  Comment,
  Link,
  SlyFaceWhitSmile,
  Like,
  ApplicationMenu,
  Search,
  Copy,
  Loading,
  Rss,
  MessageEmoji,
  AddUser,
  ReduceUser,
  MessageOne,
  AlarmClock,
  Bookmark,
  Inbox,
  LoadingFour,
  Mail,
  Lock,
  User,
  Send,
  Unlock,
  LockOne,
  ImageFiles,
  ExpandUp,
  Close,
  ArrowLeft,
  CollapseTextInput,
  Stopwatch,
  PaperMoneyTwo,
  Check,
  ChartHistogram,
  CheckCorrect,
  PeoplePlus,
  PeopleMinusOne,
  SmartOptimization,
  Save,
  Clear,
  FileText,
  History,
  Lightning,
  PeoplesTwo,
  Code,
  GoodTwo,
  Twitter,
  Bitcoin,
  Fire,
  Communication,
  WaterLevel,
  RobotOne,
  Server,
  Protection,
  DoubleDown,
  Open,
  Dislike,
  CheckOne,
  Share,
} from '@icon-park/vue-next'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component('Pin', Pin)
  nuxtApp.vueApp.component('Fireworks', Fireworks)
  nuxtApp.vueApp.component('Gift', Gift)
  nuxtApp.vueApp.component('RankingList', RankingList)
  nuxtApp.vueApp.component('Star', Star)
  nuxtApp.vueApp.component('Edit', Edit)
  nuxtApp.vueApp.component('HashtagKey', HashtagKey)
  nuxtApp.vueApp.component('Remind', Remind)
  nuxtApp.vueApp.component('InfoIcon', Info)
  nuxtApp.vueApp.component('ChartLine', ChartLine)
  nuxtApp.vueApp.component('Finance', Finance)
  nuxtApp.vueApp.component('Up', Up)
  nuxtApp.vueApp.component('Down', Down)
  nuxtApp.vueApp.component('TagOne', TagOne)
  nuxtApp.vueApp.component('MedalOne', MedalOne)
  nuxtApp.vueApp.component('Next', Next)
  nuxtApp.vueApp.component('DropDownList', DropDownList)
  nuxtApp.vueApp.component('MoreOne', MoreOne)
  nuxtApp.vueApp.component('SunOne', SunOne)
  nuxtApp.vueApp.component('Moon', Moon)
  nuxtApp.vueApp.component('ComputerOne', ComputerOne)
  nuxtApp.vueApp.component('CommentIcon', Comment)
  nuxtApp.vueApp.component('LinkIcon', Link)
  nuxtApp.vueApp.component('SlyFaceWhitSmile', SlyFaceWhitSmile)
  nuxtApp.vueApp.component('Like', Like)
  nuxtApp.vueApp.component('ApplicationMenu', ApplicationMenu)
  nuxtApp.vueApp.component('SearchIcon', Search)
  nuxtApp.vueApp.component('Copy', Copy)
  nuxtApp.vueApp.component('Loading', Loading)
  nuxtApp.vueApp.component('Rss', Rss)
  nuxtApp.vueApp.component('MessageEmoji', MessageEmoji)
  nuxtApp.vueApp.component('AddUser', AddUser)
  nuxtApp.vueApp.component('ReduceUser', ReduceUser)
  nuxtApp.vueApp.component('MessageOne', MessageOne)
  nuxtApp.vueApp.component('AlarmClock', AlarmClock)
  nuxtApp.vueApp.component('Bookmark', Bookmark)
  nuxtApp.vueApp.component('Inbox', Inbox)
  nuxtApp.vueApp.component('LoadingFour', LoadingFour)
  nuxtApp.vueApp.component('UserIcon', User)
  nuxtApp.vueApp.component('Mail', Mail)
  nuxtApp.vueApp.component('Lock', Lock)
  nuxtApp.vueApp.component('SendIcon', Send)
  nuxtApp.vueApp.component('Unlock', Unlock)
  nuxtApp.vueApp.component('LockOne', LockOne)
  nuxtApp.vueApp.component('ImageFiles', ImageFiles)
  nuxtApp.vueApp.component('ExpandUp', ExpandUp)
  nuxtApp.vueApp.component('CloseIcon', Close)
  nuxtApp.vueApp.component('ArrowLeft', ArrowLeft)
  nuxtApp.vueApp.component('CollapseTextInput', CollapseTextInput)
  nuxtApp.vueApp.component('Stopwatch', Stopwatch)
  nuxtApp.vueApp.component('PaperMoneyTwo', PaperMoneyTwo)
  nuxtApp.vueApp.component('Check', Check)
  nuxtApp.vueApp.component('ChartHistogram', ChartHistogram)
  nuxtApp.vueApp.component('CheckCorrect', CheckCorrect)
  nuxtApp.vueApp.component('PeoplePlus', PeoplePlus)
  nuxtApp.vueApp.component('PeopleMinusOne', PeopleMinusOne)
  nuxtApp.vueApp.component('SmartOptimization', SmartOptimization)
  nuxtApp.vueApp.component('SaveIcon', Save)
  nuxtApp.vueApp.component('ClearIcon', Clear)
  nuxtApp.vueApp.component('FileText', FileText)
  nuxtApp.vueApp.component('HistoryIcon', History)
  nuxtApp.vueApp.component('Lightning', Lightning)
  nuxtApp.vueApp.component('PeoplesTwo', PeoplesTwo)
  nuxtApp.vueApp.component('CodeIcon', Code)
  nuxtApp.vueApp.component('GoodTwo', GoodTwo)
  nuxtApp.vueApp.component('Twitter', Twitter)
  nuxtApp.vueApp.component('Bitcoin', Bitcoin)
  nuxtApp.vueApp.component('Fire', Fire)
  nuxtApp.vueApp.component('Communication', Communication)
  nuxtApp.vueApp.component('WaterLevel', WaterLevel)
  nuxtApp.vueApp.component('RobotOne', RobotOne)
  nuxtApp.vueApp.component('ServerIcon', Server)
  nuxtApp.vueApp.component('Protection', Protection)
  nuxtApp.vueApp.component('DoubleDown', DoubleDown)
  nuxtApp.vueApp.component('OpenIcon', Open)
  nuxtApp.vueApp.component('Dislike', Dislike)
  nuxtApp.vueApp.component('CheckOne', CheckOne)
  nuxtApp.vueApp.component('Share', Share)
})
