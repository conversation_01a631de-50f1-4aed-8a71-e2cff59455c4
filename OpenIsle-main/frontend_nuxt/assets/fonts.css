/* Maple Mono - Thin 100 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-100-normal.woff2") format("woff2");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - Thin Italic 100 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-100-italic.woff2") format("woff2");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - ExtraLight 200 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-200-normal.woff2") format("woff2");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - ExtraLight Italic 200 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-200-italic.woff2") format("woff2");
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - Light 300 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-300-normal.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - Light Italic 300 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-300-italic.woff2") format("woff2");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - Regular 400 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-400-normal.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - Italic 400 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-400-italic.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - Medium 500 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-500-normal.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - Medium Italic 500 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-500-italic.woff2") format("woff2");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - SemiBold 600 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-600-normal.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - SemiBold Italic 600 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-600-italic.woff2") format("woff2");
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - Bold 700 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-700-normal.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - Bold Italic 700 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-700-italic.woff2") format("woff2");
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* Maple Mono - ExtraBold 800 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-800-normal.woff2") format("woff2");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Maple Mono - ExtraBold Italic 800 */
@font-face {
  font-family: "Maple Mono";
  src: url("/fonts/maple-mono-800-italic.woff2") format("woff2");
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}
