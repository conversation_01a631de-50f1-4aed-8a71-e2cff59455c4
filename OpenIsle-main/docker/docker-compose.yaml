services:
  # MySQL service
  mysql:
    image: mysql:8.0
    container_name: openisle-mysql
    restart: always
    env_file:
      - ../backend/open-isle.env
      - ./.env
    ports:
      - "${MYSQL_PORT}:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ../backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    networks:
      - openisle-network

  # Java spring boot service
  springboot:
    image: maven:3.9-eclipse-temurin-17
    container_name: openisle-springboot
    working_dir: /app
    env_file:
      - ../backend/open-isle.env
      - ./.env
    environment:
      - MYSQL_URL=jdbc:mysql://mysql:${MYSQL_PORT}/${MYSQL_DATABASE}?useUnicode=yes&characterEncoding=UTF-8&useInformationSchema=true&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    ports:
      - "${SERVER_PORT}:8080"
    volumes:
      - ../backend:/app
      - maven-repo:/root/.m2
    depends_on:
      - mysql
    command: mvn clean spring-boot:run -Dmaven.test.skip=true
    networks:
      - openisle-network

networks:
  openisle-network:
    driver: bridge

volumes:
  mysql-data:
  maven-repo:
