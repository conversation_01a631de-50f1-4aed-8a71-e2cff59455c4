server.port=${SERVER_PORT:8082}

# æå¡å¨éç½®
spring.application.name=websocket-service

# RabbitMQ éç½®
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=/

# JWT éç½®
app.jwt.secret=${JWT_SECRET:jwt_sec}

# æ¥å¿éç½®
logging.level.com.openisle=${LOG_LEVEL:INFO}
logging.level.org.springframework.messaging=${MESSAGING_LOG_LEVEL:DEBUG}
logging.level.org.springframework.web.socket=${WEBSOCKET_LOG_LEVEL:DEBUG}

# ç½ç« URL éç½®
app.website-url=${WEBSITE_URL:https://www.open-isle.com}