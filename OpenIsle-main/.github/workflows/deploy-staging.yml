name: Staging CI & CD

on:
  push:
    branches: [main]
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: Deploy

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: root
          key: ${{ secrets.SSH_KEY }}
          script: bash /opt/openisle/deploy-staging.sh

  deploy-docs:
    needs: build-and-deploy
    if: ${{ success() }}
    uses: ./.github/workflows/deploy-docs.yml
    secrets: inherit
    with:
      build-id: ${{ github.run_id }}

