name: Deploy Documentation

on:
  workflow_call:
    inputs:
      build-id:
        required: false
        type: string
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Log build
        run: echo "Running documentation deployment from build ${{ inputs.build-id }}"

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install Bun dependencies
        run: bun install
        working-directory: ./docs

      - name: Generate API MDX
        run: bun run generate
        working-directory: ./docs

      - name: Build documentation
        run: bun run build
        working-directory: ./docs

      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: gh-pages
          folder: ./docs/out
