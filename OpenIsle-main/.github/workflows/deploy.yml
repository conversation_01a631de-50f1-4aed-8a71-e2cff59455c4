name: CI & CD

on:
  workflow_dispatch:
  schedule:
    - cron: "0 19 * * *"   # 每天 UTC 19:00，相当于北京时间凌晨3点

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: Deploy

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: root
          key: ${{ secrets.SSH_KEY }}
          script: bash /opt/openisle/deploy.sh
